from odps import ODPS
import pandas

class MaxCompute:
    def __init__(
            self, 
            access_id='LTAI5tMg66hcegXAvaiXPFSB', 
            access_key='******************************', 
            endpoint='http://service.cn-beijing.maxcompute.aliyun.com/api'
        ):
        self.yuapo = ODPS(access_id, access_key, 'yuapo', endpoint)
        self.yuapo_dev = ODPS(access_id, access_key, 'yuapo_dev', endpoint)

    def execute_sql(self, sql, project='yuapo'):
        if project == 'yuapo':
            with self.yuapo.execute_sql(sql).open_reader() as reader:
                return reader.to_pandas()
        elif project == 'yuapo_dev':
            with self.yuapo_dev.execute_sql(sql).open_reader() as reader:
                return reader.to_pandas()

    def get_jd(keyword, occ):
        sql = f'''
WITH info AS (
    SELECT  id AS info_id, detail, occupations_v2 AS occ_id, temp_occ_id
    FROM    yuapo.ods_gczdw_q
    LATERAL VIEW explode(split(occupations_v2, ',')) temp AS temp_occ_id
    WHERE   pt=20210807
    AND     is_check=2
    AND     LENGTH(detail) >= 100
    AND     detail RLIKE '入库员|仓库入库员'
    AND     TO_CHAR(FROM_UNIXTIME(sort_time), 'yyyymmdd') BETWEEN '20250101' AND '20250701'
),
occ AS (
    SELECT  id AS occ_id, occupation_name AS occ_name
    FROM    yuapo.ods_occupation
    WHERE   pt=20210807
    AND     deleted=0
    AND     occupation_status=1
    AND     version=2
)
SELECT  info.info_id, info.detail, info.occ_id, CONCAT_WS(',', COLLECT_SET(occ.occ_name)) AS occ_name
FROM    info
INNER JOIN  occ
ON      info.temp_occ_id = occ.occ_id
GROUP BY info.info_id, info.detail, info.occ_id;
'''
        data = self.execute_sql(sql)
        return data.to_pandas()