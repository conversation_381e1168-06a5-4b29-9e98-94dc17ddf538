<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Particle Galaxy with Nebulas</title>
    <style>
        body {
            margin: 0;
            overflow: hidden;
            background-color: #000;
        }
        canvas {
            display: block;
        }
        .info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            font-family: Arial, sans-serif;
            font-size: 14px;
            background-color: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="info">3D Particle Galaxy with Swirling Nebulas<br>Click and drag to rotate | Scroll to zoom</div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.min.js"></script>
    <script>
        // Initialize scene, camera, and renderer
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 2000);
        const renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setPixelRatio(window.devicePixelRatio);
        document.body.appendChild(renderer.domElement);

        // Set up camera position
        camera.position.set(0, 50, 150);
        camera.lookAt(0, 0, 0);

        // Add orbit controls for interaction
        const controls = new THREE.OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.dampingFactor = 0.05;
        controls.screenSpacePanning = false;
        controls.minDistance = 50;
        controls.maxDistance = 500;

        // Add ambient light
        const ambientLight = new THREE.AmbientLight(0x222244, 0.5);
        scene.add(ambientLight);

        // Add central light source (simulating galactic core)
        const coreLight = new THREE.PointLight(0xffaa88, 2, 300);
        coreLight.position.set(0, 0, 0);
        scene.add(coreLight);

        // Create galaxy parameters
        const galaxyParams = {
            count: 150000,
            size: 0.1,
            radius: 100,
            branches: 5,
            spin: 1.5,
            randomness: 0.2,
            randomnessPower: 3,
            insideColor: '#ff6030',
            outsideColor: '#1b3984',
            nebulaCount: 5,
            nebulaSize: 50
        };

        // Generate galaxy particles
        let galaxyGeometry = null;
        let galaxyMaterial = null;
        let galaxyPoints = null;

        // Generate nebula clouds
        let nebulae = [];

        // Generate the galaxy
        function generateGalaxy() {
            // Dispose of old galaxy if it exists
            if (galaxyPoints !== null) {
                galaxyGeometry.dispose();
                galaxyMaterial.dispose();
                scene.remove(galaxyPoints);
            }

            // Create new geometry
            galaxyGeometry = new THREE.BufferGeometry();
            const positions = new Float32Array(galaxyParams.count * 3);
            const colors = new Float32Array(galaxyParams.count * 3);
            const scales = new Float32Array(galaxyParams.count);
            const randomness = new Float32Array(galaxyParams.count * 3);
            const insideColor = new THREE.Color(galaxyParams.insideColor);
            const outsideColor = new THREE.Color(galaxyParams.outsideColor);

            for (let i = 0; i < galaxyParams.count; i++) {
                const i3 = i * 3;

                // Position
                const radius = Math.random() * galaxyParams.radius;
                const spinAngle = radius * galaxyParams.spin;
                const branchAngle = (i % galaxyParams.branches) / galaxyParams.branches * Math.PI * 2;

                const randomX = Math.pow(Math.random(), galaxyParams.randomnessPower) * (Math.random() < 0.5 ? 1 : -1) * galaxyParams.randomness * radius;
                const randomY = Math.pow(Math.random(), galaxyParams.randomnessPower) * (Math.random() < 0.5 ? 1 : -1) * galaxyParams.randomness * radius;
                const randomZ = Math.pow(Math.random(), galaxyParams.randomnessPower) * (Math.random() < 0.5 ? 1 : -1) * galaxyParams.randomness * radius;

                positions[i3] = Math.cos(branchAngle + spinAngle) * radius + randomX;
                positions[i3 + 1] = randomY;
                positions[i3 + 2] = Math.sin(branchAngle + spinAngle) * radius + randomZ;

                // Store randomness for animation
                randomness[i3] = randomX;
                randomness[i3 + 1] = randomY;
                randomness[i3 + 2] = randomZ;

                // Color
                const mixedColor = insideColor.clone();
                mixedColor.lerp(outsideColor, radius / galaxyParams.radius);

                colors[i3] = mixedColor.r;
                colors[i3 + 1] = mixedColor.g;
                colors[i3 + 2] = mixedColor.b;

                // Scale
                scales[i] = Math.random() * galaxyParams.size;
            }

            galaxyGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            galaxyGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
            galaxyGeometry.setAttribute('aScale', new THREE.BufferAttribute(scales, 1));
            galaxyGeometry.setAttribute('aRandomness', new THREE.BufferAttribute(randomness, 3));

            // Material
            galaxyMaterial = new THREE.PointsMaterial({
                size: galaxyParams.size,
                sizeAttenuation: true,
                depthWrite: false,
                blending: THREE.AdditiveBlending,
                vertexColors: true
            });

            // Points
            galaxyPoints = new THREE.Points(galaxyGeometry, galaxyMaterial);
            scene.add(galaxyPoints);

            // Generate nebulae
            generateNebulae();
        }

        // Generate nebula clouds
        function generateNebulae() {
            // Remove old nebulae
            nebulae.forEach(nebula => scene.remove(nebula));
            nebulae = [];

            for (let i = 0; i < galaxyParams.nebulaCount; i++) {
                // Create a nebula cloud using a sphere with custom shader
                const nebulaGeometry = new THREE.SphereGeometry(
                    galaxyParams.nebulaSize * (0.5 + Math.random() * 0.5),
                    32,
                    32
                );

                // Create a random color for this nebula
                const hue = Math.random();
                const nebulaColor = new THREE.Color().setHSL(hue, 0.7, 0.5);
                
                // Create shader material for the nebula
                const nebulaMaterial = new THREE.ShaderMaterial({
                    uniforms: {
                        time: { value: 0 },
                        color: { value: nebulaColor },
                        opacity: { value: 0.3 + Math.random() * 0.3 }
                    },
                    vertexShader: `
                        uniform float time;
                        varying vec3 vPosition;
                        varying vec2 vUv;
                        
                        void main() {
                            vPosition = position;
                            vUv = uv;
                            
                            vec3 pos = position;
                            float noise = sin(pos.x * 5.0 + time) * cos(pos.y * 5.0 + time) * sin(pos.z * 5.0 + time) * 0.1;
                            pos += normal * noise;
                            
                            gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
                        }
                    `,
                    fragmentShader: `
                        uniform vec3 color;
                        uniform float opacity;
                        uniform float time;
                        varying vec3 vPosition;
                        varying vec2 vUv;
                        
                        void main() {
                            float dist = distance(vUv, vec2(0.5));
                            float alpha = 1.0 - smoothstep(0.0, 0.5, dist);
                            
                            // Add some swirling patterns
                            float pattern = sin(vUv.x * 10.0 + time) * cos(vUv.y * 10.0 + time) * 0.5 + 0.5;
                            alpha *= pattern;
                            
                            gl_FragColor = vec4(color, alpha * opacity);
                        }
                    `,
                    transparent: true,
                    depthWrite: false,
                    blending: THREE.AdditiveBlending
                });

                const nebula = new THREE.Mesh(nebulaGeometry, nebulaMaterial);
                
                // Position the nebula randomly within the galaxy
                const radius = Math.random() * galaxyParams.radius * 0.8;
                const theta = Math.random() * Math.PI * 2;
                const phi = Math.random() * Math.PI;
                
                nebula.position.x = radius * Math.sin(phi) * Math.cos(theta);
                nebula.position.y = radius * Math.cos(phi);
                nebula.position.z = radius * Math.sin(phi) * Math.sin(theta);
                
                // Random rotation
                nebula.rotation.x = Math.random() * Math.PI;
                nebula.rotation.y = Math.random() * Math.PI;
                nebula.rotation.z = Math.random() * Math.PI;
                
                scene.add(nebula);
                nebulae.push(nebula);
            }
        }

        // Create background stars
        function createBackgroundStars() {
            const starGeometry = new THREE.BufferGeometry();
            const starCount = 5000;
            const positions = new Float32Array(starCount * 3);
            
            for (let i = 0; i < starCount * 3; i += 3) {
                positions[i] = (Math.random() - 0.5) * 2000;
                positions[i + 1] = (Math.random() - 0.5) * 2000;
                positions[i + 2] = (Math.random() - 0.5) * 2000;
            }
            
            starGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            
            const starMaterial = new THREE.PointsMaterial({
                color: 0xffffff,
                size: 0.5,
                sizeAttenuation: true
            });
            
            const stars = new THREE.Points(starGeometry, starMaterial);
            scene.add(stars);
        }

        // Initialize the scene
        generateGalaxy();
        createBackgroundStars();

        // Handle window resize
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });

        // Animation loop
        const clock = new THREE.Clock();
        let time = 0;

        function animate() {
            requestAnimationFrame(animate);
            
            const elapsedTime = clock.getElapsedTime();
            time = elapsedTime;
            
            // Update galaxy rotation
            if (galaxyPoints) {
                galaxyPoints.rotation.y = elapsedTime * 0.05;
                
                // Animate particles for subtle movement
                const positions = galaxyGeometry.attributes.position.array;
                const randomness = galaxyGeometry.attributes.aRandomness.array;
                
                for (let i = 0; i < galaxyParams.count; i++) {
                    const i3 = i * 3;
                    
                    // Add subtle oscillation based on stored randomness
                    positions[i3] += Math.sin(elapsedTime + i * 0.1) * 0.01 * randomness[i3] * 0.1;
                    positions[i3 + 1] += Math.cos(elapsedTime + i * 0.1) * 0.01 * randomness[i3 + 1] * 0.1;
                    positions[i3 + 2] += Math.sin(elapsedTime + i * 0.05) * 0.01 * randomness[i3 + 2] * 0.1;
                }
                
                galaxyGeometry.attributes.position.needsUpdate = true;
            }
            
            // Update nebulae
            nebulae.forEach((nebula, index) => {
                nebula.rotation.x += 0.0003 * (index % 2 === 0 ? 1 : -1);
                nebula.rotation.y += 0.0002 * (index % 3 === 0 ? 1 : -1);
                nebula.rotation.z += 0.0001 * (index % 4 === 0 ? 1 : -1);
                
                // Update shader time uniform
                nebula.material.uniforms.time.value = elapsedTime;
            });
            
            // Update controls
            controls.update();
            
            // Render
            renderer.render(scene, camera);
        }

        animate();
    </script>
</body>
</html>
