{"cells": [{"cell_type": "code", "execution_count": null, "id": "5de18ed7", "metadata": {}, "outputs": [], "source": ["from tool.odps import MaxCompute\n", "\n", "mc = MaxCompute()"]}, {"cell_type": "code", "execution_count": null, "id": "92f6d7f5", "metadata": {}, "outputs": [], "source": ["import pandas\n", "\n", "sample = pandas.read_csv('./data/age_salary_sample.csv')\n", "ids = tuple(sample['info_id'].to_list())"]}, {"cell_type": "code", "execution_count": null, "id": "45914c21", "metadata": {}, "outputs": [], "source": ["if ids:\n", "    ids_str = ','.join([f\"'{_id}'\" if isinstance(_id, str) else str(_id) for _id in ids])\n", "    where_clause = f\"AND id in ({ids_str})\"\n", "else:\n", "    where_clause = \"AND 1=0\" # No results if ids is empty\n", "\n", "sql = f'''\n", "SELECT  id AS info_id, title, detail, GET_JSON_OBJECT(ext,'$.job_complete_v2') AS job_complete_v2\n", "FROM    yuapo.ods_gczdw_q\n", "WHERE   pt=20210807\n", "AND     is_check=2\n", "{where_clause}\n", "'''\n", "\n", "data = mc.execute_sql(sql)"]}], "metadata": {"kernelspec": {"display_name": "yup<PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}