import json_repair
import time
import aiohttp
import pandas
import asyncio
from tqdm.asyncio import tqdm_asyncio
from tqdm.auto import tqdm
from collections import defaultdict
from tool.api import newapi_chat_async, local_chat_async


system_prompt = """{
    "task": "<query>是用户在招聘平台上的搜索词条，请你判断该词条是否含有年龄或薪资的意图信息",
    "json output format": {
        "label": "yes" or "no",
        "reason": ""
    }
}"""


async def concurrency(queries, model):
    user_prompts = list()
    for query in queries:
        user_prompt = '{"query": "%s"}' % query
        user_prompts.append(user_prompt)
    try:
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60)) as session:
            tasks = [local_chat_async(session, system_prompt, user_prompt) for user_prompt in user_prompts]
            # tasks = [newapi_chat_async(session, system_prompt, user_prompt, model) for user_prompt in user_prompts]
            results = await tqdm_asyncio.gather(*tasks)
        return list(results)
    except Exception:
        return None


async def main(file):
    model = 'deepseek'
    chunksize = 10
    reader = pandas.read_csv(file, chunksize=chunksize)
    total = sum([1 for _ in reader])
    reader.close()

    data = list()
    reader = pandas.read_csv(file, chunksize=chunksize)
    for chunk in tqdm(reader, total=total):
        queries = chunk['query_no_address'].to_list()
        results = await concurrency(queries, model)
        if results is None:
            continue

        time.sleep(1)

        for query, result in zip(queries, results):
            try:
                result = json_repair.loads(result)
                if result['label'] == 'yes':
                    data.append(query)
            except Exception:
                continue

    pandas.DataFrame(data, columns=['query']).to_csv(f'./data/query_sample.csv', index=False, lineterminator='\r\n')


if __name__ == '__main__':
    asyncio.run(main('./data/query_occ_20250703.csv'))