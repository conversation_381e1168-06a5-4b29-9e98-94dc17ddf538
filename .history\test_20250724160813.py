import pandas as pd
import aiohttp
import asyncio
import json_repair
import ast
import requests
from tqdm import tqdm
import math
import pandas as pd
import numpy as np
import math
import json
import ast
import asyncio
import aiohttp
from tqdm.asyncio import tqdm_asyncio
from tqdm import tqdm
import json_repair
import json
API_DOUBAO_KEY = '49876cc0-f5b7-42e2-8c70-001db946ab6d'
API_DOUBAO_HEADERS = {'Content-Type': 'application/json', 'Authorization': f'Bearer {API_DOUBAO_KEY}'}
API_DOUBAO_CHAT_URL = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions'
API_DOUBAO_CHAT_MODEL_new='ep-20250308100038-gb8qd'


API_DOUBAO_KEY = '49876cc0-f5b7-42e2-8c70-001db946ab6d'
API_DOUBAO_HEADERS = {'Content-Type': 'application/json', 'Authorization': f'Bearer {API_DOUBAO_KEY}'}
API_DOUBAO_CHAT_URL = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions'
system_prompt= '''{
    "Role": "白领招聘领域专家",
    "Task": "仔细理解<information>中白领工人的求职描述内容。从<candidate>数据所包含的 ' 工种 ' 标签里，推荐与求职描述相匹配的职位标签。对于仅存在微弱或模糊关联的标签，不要纳入推荐结果内。",
    "Restriction1": " 求职描述<information>中，会包含如使用材料、工具、具体工作内容、所需技能等有用信息，也会存在工作地点、时间与工种无关或无意义的信息。你需要精准提取有用信息的关键内容，据此推荐工种标签，同时过滤掉无用信息。",
    "Restriction2": "在处理以下任务时，特别注意：若<information>提到的工种类型宽泛，而<candidate>中有多个细分标签，必须必须将所有符合该宽泛类型的细分标签全部列入推荐结果。",
    "Restriction3": "<candidate>数据中的 ' 工种描述 ' 是对各工种的详细说明，可参考 ' 工种描述 ' 来判断求职描述与工种的关联性，但最终输出结果中不得包含 ' 工种描述 ' 的具体文字。",
    "Restriction4":"最终输出以元组列表的格式输出,元组中有两个元素,第一个元素为推荐工种,第二个元素为置信度分数(范围:0.0-1.0)",
    "Json Output Format": {
        "result": []
    }
}'''


# 同步函数：检查字符串是否表示NaN
def is_nan_string(s):
    try:
        float_value = float(s)
        return math.isnan(float_value)
    except ValueError:
        return False


# 同步函数：根据知识图谱召回重构工作描述
def restruct_job_describe_byknowledge_graph_recall(candidate):
    job_describe = pd.read_csv(r"D:\文档\知识图谱V1迭代\20250523\job_describe_graph_20250523.csv")
    total_list = []
    for i in range(len(candidate)):
        try:
            des_job = job_describe[job_describe['job'] == candidate[i]]['job_describe'].item()
        except ValueError:
            des_job = ""

        if is_nan_string(des_job):
            des_job = ""
        dic = {"工种": candidate[i], "工种描述": des_job.replace("\n", "").replace(" ", "")}
        total_list.append(json.dumps(dic, ensure_ascii=False))
    return total_list


# 异步函数：调用豆包API
async def api_doubao_chat_async(session, system_prompt, user_prompt, API_DOUBAO_CHAT_URL, API_DOUBAO_HEADERS,
                                API_DOUBAO_CHAT_MODEL_new):
    data = {
        'model': API_DOUBAO_CHAT_MODEL_new,
        'temperature': 0.001,
        'messages': [
            {'role': 'system', 'content': system_prompt},
            {'role': 'user', 'content': user_prompt}
        ],
        'response_format': {'type': 'json_object'}
    }

    max_retries = 3
    for attempt in range(max_retries):
        try:
            async with session.post(API_DOUBAO_CHAT_URL, headers=API_DOUBAO_HEADERS, json=data, timeout=30) as response:
                result = await response.json()
                return result['choices'][0]['message']['content']
        except Exception as e:
            if attempt == max_retries - 1:
                print(f"请求失败，已达到最大重试次数: {e}")
                return None
            wait_time = 2 ** attempt
            print(f"请求失败，{wait_time}秒后重试 ({attempt + 1}/{max_retries}): {e}")
            await asyncio.sleep(wait_time)


# 异步处理单个数据行
async def process_row(session, row, system_prompt, API_DOUBAO_CHAT_URL, API_DOUBAO_HEADERS, API_DOUBAO_CHAT_MODEL_new,
                      semaphore):
    async with semaphore:  # 使用信号量控制并发
        if pd.isna(row['llm_occupation_name']):
            llm_job = []
        else:
            llm_job = row['llm_occupation_name'].split(",")

        if pd.isna(row['occupation_names']):