from odps import ODPS
import pandas

class MaxCompute:
    def __init__(
            self, 
            access_id='LTAI5tMg66hcegXAvaiXPFSB', 
            access_key='******************************', 
            endpoint='http://service.cn-beijing.maxcompute.aliyun.com/api'
        ) -> None:
        self.yuapo = ODPS(access_id, access_key, 'yuapo', endpoint)
        self.yuapo_dev = ODPS(access_id, access_key, 'yuapo_dev', endpoint)

    def execute_sql(self, sql, project='yuapo') -> pandas.DataFrame:
        if project == 'yuapo':
            with self.yuapo.execute_sql(sql).open_reader() as reader:
                return reader.to_pandas()
        elif project == 'yuapo_dev':
            with self.yuapo_dev.execute_sql(sql).open_reader() as reader:
                return reader.to_pandas()
        