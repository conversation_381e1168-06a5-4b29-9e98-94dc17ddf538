{"python.analysis.autoImportCompletions": true, "python.analysis.typeCheckingMode": "basic", "sqltools.connections": [{"previewLimit": 100, "driver": "MaxCompute", "name": "yup<PERSON>", "group": "MaxCompute", "accessId": "LTAI5tE5yQcCmMRqMnpoEhcz", "accessKey": "******************************", "endpoint": "http://service.cn-beijing.maxcompute.aliyun.com/api", "project": "yuapo_dev"}], "sqltools.useNodeRuntime": true, "local-history.browser.descending": true, "cursorpyright.analysis.autoImportCompletions": true, "cursorpyright.analysis.typeCheckingMode": "basic", "Codegeex.CompletionDelay": 0.5, "Codegeex.Chat.fontSize": 12, "Codegeex.CommitMessageStyle": "ConventionalCommits", "Codegeex.RepoIndex": true, "trae.tab.enableRename": false, "trae.tab.enableAutoImport": false, "python.languageServer": "None"}