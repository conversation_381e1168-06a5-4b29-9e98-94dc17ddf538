<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三体运动 - 3D粒子星云</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        
        #canvas {
            display: block;
            cursor: grab;
        }
        
        #canvas:active {
            cursor: grabbing;
        }
        
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: #fff;
            font-size: 14px;
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
            user-select: none;
        }
        
        #controls {
            position: absolute;
            top: 10px;
            right: 10px;
            color: #fff;
            font-size: 12px;
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
        }
        
        label {
            display: block;
            margin: 5px 0;
        }
        
        input[type="range"] {
            width: 100px;
            vertical-align: middle;
        }
    </style>
</head>
<body>
    <canvas id="canvas"></canvas>
    <div id="info">
        <h3>三体运动模拟</h3>
        <p>鼠标拖动旋转视角</p>
        <p>滚轮缩放</p>
    </div>
    <div id="controls">
        <label>
            粒子数量: <span id="particleCount">5000</span>
            <input type="range" id="particleSlider" min="1000" max="10000" value="5000" step="1000">
        </label>
        <label>
            旋转速度: <span id="rotationSpeed">1.0</span>
            <input type="range" id="rotationSlider" min="0" max="2" value="1" step="0.1">
        </label>
        <label>
            光照强度: <span id="lightIntensity">1.0</span>
            <input type="range" id="lightSlider" min="0" max="2" value="1" step="0.1">
        </label>
    </div>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // 设置画布大小
        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);
        
        // 三体系统参数
        const G = 0.1; // 引力常数
        const bodies = [
            { x: -200, y: 0, z: 0, vx: 0, vy: 0.5, vz: 0, mass: 100, color: '#ff6b6b' },
            { x: 200, y: 0, z: 0, vx: 0, vy: -0.5, vz: 0, mass: 100, color: '#4ecdc4' },
            { x: 0, y: 300, z: 0, vx: -0.5, vy: 0, vz: 0, mass: 100, color: '#ffe66d' }
        ];
        
        // 粒子系统
        let particles = [];
        let particleCount = 5000;
        
        // 相机参数
        let camera = {
            x: 0, y: 0, z: 800,
            rotX: 0, rotY: 0,
            zoom: 1
        };
        
        // 鼠标控制
        let mouseDown = false;
        let lastMouseX = 0;
        let lastMouseY = 0;
        
        // 光照
        let lightIntensity = 1;
        let rotationSpeed = 1;
        
        // 初始化粒子
        function initParticles() {
            particles = [];
            for (let i = 0; i < particleCount; i++) {
                // 在三个天体周围随机分布粒子
                const bodyIndex = Math.floor(Math.random() * 3);
                const body = bodies[bodyIndex];
                const angle = Math.random() * Math.PI * 2;
                const radius = 50 + Math.random() * 100;
                
                particles.push({
                    x: body.x + Math.cos(angle) * radius,
                    y: body.y + Math.sin(angle) * radius,
                    z: body.z + (Math.random() - 0.5) * 100,
                    vx: (Math.random() - 0.5) * 0.5,
                    vy: (Math.random() - 0.5) * 0.5,
                    vz: (Math.random() - 0.5) * 0.5,
                    size: Math.random() * 3 + 1,
                    color: body.color,
                    alpha: Math.random() * 0.5 + 0.3
                });
            }
        }
        
        // 更新三体运动
        function updateBodies() {
            const dt = 0.1;
            
            // 计算引力
            for (let i = 0; i < bodies.length; i++) {
                let fx = 0, fy = 0, fz = 0;
                
                for (let j = 0; j < bodies.length; j++) {
                    if (i !== j) {
                        const dx = bodies[j].x - bodies[i].x;
                        const dy = bodies[j].y - bodies[i].y;
                        const dz = bodies[j].z - bodies[i].z;
                        const dist = Math.sqrt(dx * dx + dy * dy + dz * dz);
                        const force = G * bodies[i].mass * bodies[j].mass / (dist * dist);
                        
                        fx += force * dx / dist;
                        fy += force * dy / dist;
                        fz += force * dz / dist;
                    }
                }
                
                // 更新速度
                bodies[i].vx += fx / bodies[i].mass * dt;
                bodies[i].vy += fy / bodies[i].mass * dt;
                bodies[i].vz += fz / bodies[i].mass * dt;
            }
            
            // 更新位置
            for (let body of bodies) {
                body.x += body.vx * dt;
                body.y += body.vy * dt;
                body.z += body.vz * dt;
            }
        }
        
        // 更新粒子
        function updateParticles() {
            for (let particle of particles) {
                // 受三体引力影响
                let fx = 0, fy = 0, fz = 0;
                
                for (let body of bodies) {
                    const dx = body.x - particle.x;
                    const dy = body.y - particle.y;
                    const dz = body.z - particle.z;
                    const dist = Math.sqrt(dx * dx + dy * dy + dz * dz);
                    const force = G * body.mass / (dist * dist + 100); // 添加软参数避免奇点
                    
                    fx += force * dx / dist;
                    fy += force * dy / dist;
                    fz += force * dz / dist;
                }
                
                // 更新速度
                particle.vx += fx * 0.01;
                particle.vy += fy * 0.01;
                particle.vz += fz * 0.01;
                
                // 速度衰减
                particle.vx *= 0.99;
                particle.vy *= 0.99;
                particle.vz *= 0.99;
                
                // 更新位置
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.z += particle.vz;
                
                // 边界检查
                if (Math.abs(particle.x) > 500) particle.vx *= -0.5;
                if (Math.abs(particle.y) > 500) particle.vy *= -0.5;
                if (Math.abs(particle.z) > 500) particle.vz *= -0.5;
            }
        }
        
        // 3D到2D投影
        function project3D(x, y, z) {
            // 应用相机旋转
            const cosX = Math.cos(camera.rotX);
            const sinX = Math.sin(camera.rotX);
            const cosY = Math.cos(camera.rotY);
            const sinY = Math.sin(camera.rotY);
            
            // 绕Y轴旋转
            const x1 = x * cosY - z * sinY;
            const z1 = x * sinY + z * cosY;
            
            // 绕X轴旋转
            const y1 = y * cosX - z1 * sinX;
            const z2 = y * sinX + z1 * cosX;
            
            // 透视投影
            const scale = camera.zoom * 400 / (400 + z2);
            return {
                x: canvas.width / 2 + x1 * scale,
                y: canvas.height / 2 + y1 * scale,
                scale: scale
            };
        }
        
        // 计算光照
        function calculateLighting(x, y, z, color) {
            // 简单的点光源光照
            const lightPos = { x: 0, y: 0, z: 0 };
            const dx = lightPos.x - x;
            const dy = lightPos.y - y;
            const dz = lightPos.z - z;
            const dist = Math.sqrt(dx * dx + dy * dy + dz * dz);
            const intensity = Math.min(1, lightIntensity * 500 / (dist + 100));
            
            // 将颜色转换为RGB
            const r = parseInt(color.substr(1, 2), 16);
            const g = parseInt(color.substr(3, 2), 16);
            const b = parseInt(color.substr(5, 2), 16);
            
            // 应用光照
            const newR = Math.floor(r * intensity);
            const newG = Math.floor(g * intensity);
            const newB = Math.floor(b * intensity);
            
            return `rgb(${newR}, ${newG}, ${newB})`;
        }
        
        // 渲染
        function render() {
            ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 排序粒子（从远到近）
            const sortedParticles = [...particles].sort((a, b) => {
                const aZ = a.z * Math.cos(camera.rotY) - a.x * Math.sin(camera.rotY);
                const bZ = b.z * Math.cos(camera.rotY) - b.x * Math.sin(camera.rotY);
                return bZ - aZ;
            });
            
            // 渲染粒子
            for (let particle of sortedParticles) {
                const pos = project3D(particle.x, particle.y, particle.z);
                const size = particle.size * pos.scale * 0.01;
                
                if (size > 0.1) {
                    const color = calculateLighting(particle.x, particle.y, particle.z, particle.color);
                    ctx.globalAlpha = particle.alpha * pos.scale * 0.01;
                    ctx.fillStyle = color;
                    ctx.beginPath();
                    ctx.arc(pos.x, pos.y, size, 0, Math.PI * 2);
                    ctx.fill();
                }
            }
            
            // 渲染天体
            for (let body of bodies) {
                const pos = project3D(body.x, body.y, body.z);
                const size = 20 * pos.scale * 0.01;
                
                if (size > 1) {
                    const color = calculateLighting(body.x, body.y, body.z, body.color);
                    ctx.globalAlpha = 1;
                    ctx.fillStyle = color;
                    ctx.beginPath();
                    ctx.arc(pos.x, pos.y, size, 0, Math.PI * 2);
                    ctx.fill();
                    
                    // 添加光晕效果
                    const gradient = ctx.createRadialGradient(pos.x, pos.y, 0, pos.x, pos.y, size * 3);
                    gradient.addColorStop(0, color);
                    gradient.addColorStop(1, 'transparent');
                    ctx.fillStyle = gradient;
                    ctx.beginPath();
                    ctx.arc(pos.x, pos.y, size * 3, 0, Math.PI * 2);
                    ctx.fill();
                }
            }
            
            ctx.globalAlpha = 1;
        }
        
        // 动画循环
        function animate() {
            updateBodies();
            updateParticles();
            
            // 自动旋转
            camera.rotY += 0.005 * rotationSpeed;
            
            render();
            requestAnimationFrame(animate);
        }
        
        // 鼠标控制
        canvas.addEventListener('mousedown', (e) => {
            mouseDown = true;
            lastMouseX = e.clientX;
            lastMouseY = e.clientY;
        });
        
        canvas.addEventListener('mousemove', (e) => {
            if (mouseDown) {
                const deltaX = e.clientX - lastMouseX;
                const deltaY = e.clientY - lastMouseY;
                
                camera.rotY += deltaX * 0.01;
                camera.rotX += deltaY * 0.01;
                
                lastMouseX = e.clientX;
                lastMouseY = e.clientY;
            }
        });
        
        canvas.addEventListener('mouseup', () => {
            mouseDown = false;
        });
        
        canvas.addEventListener('wheel', (e) => {
            e.preventDefault();
            camera.zoom *= e.deltaY > 0 ? 0.9 : 1.1;
            camera.zoom = Math.max(0.1, Math.min(5, camera.zoom));
        });
        
        // 控制面板
        document.getElementById('particleSlider').addEventListener('input', (e) => {
            particleCount = parseInt(e.target.value);
            document.getElementById('particleCount').textContent = particleCount;
            initParticles();
        });
        
        document.getElementById('rotationSlider').addEventListener('input', (e) => {
            rotationSpeed = parseFloat(e.target.value);
            document.getElementById('rotationSpeed').textContent = rotationSpeed.toFixed(1);
        });
        
        document.getElementById('lightSlider').addEventListener('input', (e) => {
            lightIntensity = parseFloat(e.target.value);
            document.getElementById('lightIntensity').textContent = lightIntensity.toFixed(1);
        });
        
        // 初始化并开始动画
        initParticles();
        animate();
    </script>
</body>
</html>
