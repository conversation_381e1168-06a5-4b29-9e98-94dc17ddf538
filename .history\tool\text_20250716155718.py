import re
import opencc
import unicodedata
import hanlp
import jieba

converter = opencc.OpenCC('t2s.json')   
char = r'0-9a-zA-Z \u4e00-\u9fff'   # 数字、英文、空格、汉字
punctuation = r"""`!@#$%^&*()_+-=·！@#￥%……&*（）—[]{};:'"\|,.<>/?【】；：‘’“”、，。《》、？"""   # 标点符号
pattern = f'[^{char}{punctuation}]' 
model = hanlp.load(hanlp.pretrained.mtl.OPEN_TOK_POS_NER_SRL_DEP_SDP_CON_ELECTRA_BASE_ZH)
# hanlp.load(hanlp.pretrained.mtl.CLOSE_TOK_POS_NER_SRL_DEP_SDP_CON_ELECTRA_BASE_ZH)
# hanlp.load(hanlp.pretrained.mtl.CLOSE_TOK_POS_NER_SRL_DEP_SDP_CON_ERNIE_GRAM_ZH)


def preprocess(raw):
    # 正则过滤
    raw = re.sub(pattern, ' ', raw)
    raw = re.sub(r'\s+', ' ', raw)

    # 合法 unicode 字符
    text = ''
    for item in raw.strip():
        try:
            if unicodedata.name(item):
                text += item
        except ValueError:
            continue
    
    # 全角 → 半角
    text = unicodedata.normalize('NFKC', text)

    # 繁体字 → 简体字
    text = converter.convert(text)

    # 删除地名
    # result = model(text, tasks='ner*')
    # for item in result['ner']:
    #     if item[1] in ['LOCATION', 'ORGANIZATION']:
    #         text = text.replace(item[0], '')
    return text


def forwardMM(words, text, tokenize=False):
    '''
    正向最大匹配
    https://mp.weixin.qq.com/s?__biz=MzU5NzkyMTYzNw==&mid=2247506975&idx=1&sn=b065536e68ced93321022053f6447b50&chksm=fe4e8615c9390f035455c4ff3f8a0108daf5bbff68394d5598dba3a8026a47af36825adeb281#rd

    Args:
        words (list): 
        text (str): 
        tokenize (bool, optional): Defaults to False.
    '''
    if tokenize:
        text = jieba.lcut(text)

    result = list()
    if len(words) != 0:
        max_len = max([len(word) for word in words])    
        start, length = 0, len(text)
        while start < length:
            end = length if start + max_len > length else start + max_len
            for _ in range(max_len):
                if tokenize:
                    sub_list = text[start:end]
                    sub_str = ''.join(sub_list)
                    if sub_str in words:
                        result.append(sub_str)
                        start = end
                        break
                    if len(sub_list) == 1:
                        start = end
                        break
                else:
                    sub_str = text[start:end]
                    if sub_str in words:
                        result.append(sub_str)
                        start = end
                        break
                    if len(sub_str) == 1:
                        start = end
                        break
                end -= 1
    return result


def backwardMM(words, text, tokenize=False):
    '''
    逆向最大匹配
    https://mp.weixin.qq.com/s?__biz=MzU5NzkyMTYzNw==&mid=2247506975&idx=1&sn=b065536e68ced93321022053f6447b50&chksm=fe4e8615c9390f035455c4ff3f8a0108daf5bbff68394d5598dba3a8026a47af36825adeb281#rd
    
    Args:
        words (list): 
        text (str):  
        tokenize (bool, optional): Defaults to False.
    '''
    if tokenize:
        text = jieba.lcut(text)

    result = list()
    if len(words) != 0:
        max_len = max([len(word) for word in words])    
        start = len(text)
        while start > 0:
            end = 0 if start - max_len < 0 else start - max_len
            for _ in range(max_len):
                if tokenize:
                    sub_list = text[end:start]
                    sub_str = ''.join(sub_list)
                    if sub_str in words:
                        result.append(sub_str)
                        start = end
                        break
                    if len(sub_list) == 1:
                        start = end
                        break
                else:
                    sub_str = text[end:start]
                    if sub_str in words:
                        result.append(sub_str)
                        start = end
                        break
                    if len(sub_str) == 1:
                        start = end
                        break
                end += 1
    return result


if __name__ == '__main__':
    pass