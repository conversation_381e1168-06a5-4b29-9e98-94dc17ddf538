import pandas
import requests
import json
import json_repair
from tqdm.auto import tqdm
from itertools import chain
from tool.odps import <PERSON><PERSON><PERSON>pute
from tool.text import preprocess
from tool.api import newapi_chat
from sentence_transformers import SentenceTransformer, util
from fastapi import FastAPI
from fastapi.responses import StreamingResponse
import async<PERSON>


def get_occ_info_by_sql(query):
    mc = MaxCompute()
    sql = '''
WITH info AS (
    SELECT  id AS info_id, detail, occupations_v2 AS occ_id, temp_occ_id
    FROM    yuapo.ods_gczdw_q
    LATERAL VIEW explode(split(occupations_v2, ',')) temp AS temp_occ_id
    WHERE   pt=20210807
    AND     is_check=2
    AND     LENGTH(detail)>=100
    AND     detail RLIKE '%s'
    AND     TO_CHAR(FROM_UNIXTIME(sort_time), 'yyyymmdd') BETWEEN '20250101' AND '20250701'
),
occ AS (
    SELECT  id AS occ_id, occupation_name AS occ_name
    FROM    yuapo.ods_occupation
    WHERE   pt=20210807
    AND     deleted=0
    AND     occupation_status=1
    AND     version=2
)
SELECT  info.info_id, info.detail, info.occ_id, CONCAT_WS(',', COLLECT_SET(occ.occ_name)) AS occ_name
FROM    info
INNER JOIN  occ
ON      info.temp_occ_id = occ.occ_id
GROUP BY info.info_id, info.detail, info.occ_id;
''' % query
    data = mc.execute_sql(sql)
    return data


def filter_by_cluster(text):
    model = SentenceTransformer('training_faq_pytorch-bert-chinese', device='cpu')
    embeddings = model.encode(text, show_progress_bar=True, normalize_embeddings=True)

    results = list()
    clusters = util.community_detection(embeddings, 0.85, 2, show_progress_bar=True)
    clustered_indices = set(chain.from_iterable(clusters))
    unclustered_indices = [i for i in range(len(text)) if i not in clustered_indices]
    for cluster in clusters:
        result = list()
        for index in cluster:
            result.append(text[index])
        longest = max(result, key=len)
        results.append(longest)
    for i in unclustered_indices:
        results.append(text[i])
    return results


def get_web_info(query):
    url = f"https://dashscope.aliyuncs.com/api/v1/apps/aa5d7d71c35047f692e8e336bc42172c/completion"
    headers = {
        "Authorization": "Bearer sk-2cb3c51a2bb24a9eb8200b6b1b50305a",
        "Content-Type": "application/json"
    }
    data = {
        "input": {
            "prompt": query
        }
    }
    response = requests.post(url, headers=headers, json=data)
    response.raise_for_status()
    result = response.json()
    return result["output"]["text"]


def get_local_info(query, text):
    system_prompt = """# Background
在一个招聘平台中，一个职业的精确定义是其核心竞争力之一。通过对**工作内容**和**资质要求**的精细化定义，招聘平台能够为求职者提供更精准的职位匹配，为招聘方提供更高效的候选人筛选，从而提升整体招聘效率和用户满意度。

# Role
你是一位资深的人力资源(HR)分析师和行业专家，同时也是一位善于用大白话把复杂事情说清楚的“职场老司机”，专注于招聘信息分析与职位定义精细化。

## Skills
- 核心职责提炼: 能准确识别**关键工作内容(用什么工具做什么事)**，忽略大多数职位都适用的描述
- 资质要求评估: 能准确区分**关键资质要求(需要什么证书、能力)**，忽略大多数职位都适用的描述
- 行业术语解释: 能对专业术语进行通俗的解释
- 需求理解: 准确把握不同用户群体的理解能力
- 文化适应: 适应不同教育背景用户的表达需求

## Rules
- 准确性: 必须忠实反映原始招聘信息的核心内容，不加入个人主观判断和偏好
- 实用性: 职业定义要能帮助用户快速判断是否适合该职位
- **简明扼要: 语言高度凝练、内容直达核心，能用一句话说明的就不要废话**
(招聘平台上的用户在查看职业的定义时，**只会关注该职业的硬性能力**，对于大多数职位都适用的描述根本不在乎，**因为大家都懂，不需要你再重复废话**)
(哪些是废话？参考：个人要求(性格、年龄、学历、身体状况、精神意识、态度)，通用工作要求(经验、服从安排、遵守规定、工作强度大)，其它类似的描述)

## Workflows
1.通读原始招聘信息，理解整体内容
2.分析提炼**关键工作内容**、评估确定**关键资质要求**
3.去噪、聚焦，删除冗余信息
4.整理、检查信息，返回精简的结果

## Output
- JSON格式
{
   "word": "<word>",
   "names": ["其它同义名称"],
   "description": ["工作内容"],
   "requirement": ["资质要求"],
   "jargon": [{"专业术语": "解释"}]
}

# TODO
现在，我会提供一个来源于用户搜索的职业名称<word>和大量来源于招聘平台的招聘信息<detail>，请你遵守上述Rules，运用Skills，按照Workflows执行任务，然后直接返回JSON结果即可，不需要解释。
"""

    user_prompt = """{
   "word": "%s",
   "detail": %s
}""" % (query, text)
    local_info = newapi_chat(system_prompt, user_prompt, 'deepseek')
    local_info = json_repair.loads(local_info)
    local_info = json.dumps(local_info, ensure_ascii=False, indent=4)
    return local_info


def get_combined_info(query, web_info, local_info):
    system_prompt = """# Background
在一个招聘平台中，一个职业的精确定义是其核心竞争力之一。通过对**工作内容**和**资质要求**的精细化定义，招聘平台能够为求职者提供更精准的职位匹配，为招聘方提供更高效的候选人筛选，从而提升整体招聘效率和用户满意度。

# Role
你是一位资深的人力资源(HR)分析师和行业专家，同时也是一位善于用大白话把复杂事情说清楚的“职场老司机”，专注于招聘信息分析与职位定义。

## Context
- <word>是来源于用户搜索的职业名称
- <web_info>是来源于联网搜索的、相对宽泛的职业定义
- <local_info>是来源于真实招聘平台、更侧重实际市场需求的职业定义

## Rules
- 准确性: 必须忠实反映原始信息的核心内容，不加入个人主观判断和偏好
- 实用性: 职业定义要能帮助用户快速判断是否适合该职位
- **简明扼要: 语言高度凝练、内容直达核心，能用一句话说明的就不要废话**
(招聘平台上的用户在查看职业的定义时，**只会关注该职业的硬性能力**，对于大多数职位都适用的描述根本不在乎，**因为大家都懂，不需要你再重复废话**)
(哪些是废话？参考：个人要求(性格、年龄、学历、身体状况、精神意识、态度)，通用工作要求(经验、服从安排、遵守规定、工作强度大)，其它类似的描述)

## Workflows
目标：为招聘平台的用户撰写一份清晰、精炼、一眼就能看懂的“职位说明书”
1.理解并分析<web_info>和<local_info>，提取关键信息点
2.基于求同存异原则，优先采用两个信息源中都提到的信息，有冲突的信息需要添加备注
3.去噪、聚焦，删除冗余信息
4.整理、检查信息，返回精简的结果

## Output
- JSON格式
{
    "word": "<word>",
    "common_name": "通用名称",
    "description": ["工作内容"],
    "requirement": ["资质要求"],
    "jargon": [{"专业术语(key)": "解释(value)"}]
}

# TODO
现在，我会提供`Context`中的<word>、<web_info>、<local_info>，请你遵守上述Rules，按照Workflows执行任务，然后直接返回JSON结果即可，不需要解释。
"""

    user_prompt = """{
    "word": "%s",
    "web_info": %s,
    "local_info": %s
}""" % (query, web_info, local_info)
    combined_info = newapi_chat(system_prompt, user_prompt, 'deepseek')
    combined_info = json_repair.loads(combined_info)
    combined_info = json.dumps(combined_info, ensure_ascii=False, indent=4)
    return combined_info


async def process_occ_meaning(query: str):
    yield f'🐟 获取鱼泡招工信息\n'
    await asyncio.sleep(0)
    data = get_occ_info_by_sql(query)
    yield f'💡 获取到{len(data)}条数据\n'
    await asyncio.sleep(0)
    query2occ = pandas.read_csv('./data/query_occ.csv')
    occ = query2occ[query2occ['query'] == query]['occ'].item()
    yield f'💡 根据query2occ文件, {query}对应工种为{occ}\n'
    await asyncio.sleep(0)
    data = data[data['occ_name'] == occ]
    detail = data['detail'].to_list()
    yield f'💡 文本预处理\n'
    await asyncio.sleep(0)
    text = set()
    for item in detail:
        text.add(preprocess(item))
    yield f'🀄文本向量化+聚类🧩\n'
    await asyncio.sleep(0)
    text = filter_by_cluster(list(text))
    yield f'💡 过滤后, 剩余{len(text)}条有效数据\n'
    await asyncio.sleep(0)
    # yield f'🤖阿里DeepSeek+联网搜索🌐 生成工种含义\n'
    # web_info = get_web_info(query)
    # yield f'{web_info}\n'
    # yield f'🤖字节DeepSeek+本地信息📚 生成工种含义\n'
    # local_info = get_local_info(query, text)
    # yield f'{local_info}\n'
    # yield f'🤖字节DeepSeek 生成最终含义\n'
    # combined_info = get_combined_info(query, web_info, local_info)
    # yield f'{combined_info}\n'
    yield f'✅ Done\n'
    await asyncio.sleep(0)


app = FastAPI()

@app.get('/get_occ_meaning')
async def get_occ_meaning(query: str):
    return StreamingResponse(
        process_occ_meaning(query),
        media_type="text/plain"
    )


if __name__ == '__main__':
    import uvicorn
    uvicorn.run(app, host='************')