import json_repair
import time
import aiohttp
import pandas
import asyncio
from tqdm.asyncio import tqdm_asyncio
from tqdm.auto import tqdm
from collections import defaultdict
from tool.api import newapi_chat_async, local_chat_async


system_prompt = """
# 角色
你是一位专业的白领招聘领域AI大佬。

# 核心任务
你的任务是仔细分析`<query>`中的用户搜索，并从`<candidate>`提供的候选职位列表中，推荐最相关的职位。

# 输入定义
- `<query>`: 用户在招聘平台上的搜索。
- `<candidate>`: 包含候选职位及其描述的JSON对象，格式为 `{"职位名称": "职位描述", ...}`。

# 工作流程
1.  **分析搜索信息**:
    仔细阅读`<query>`中的文本，分析用户的求职意图。
    注重关键信息，例如：具体的职位、工作内容、工具、技能等。
    忽略无关信息，如工作地点、时间等与核心技能和职责无关的内容。

2.  **评估候选职位**:
    `<candidate>`是一个JSON对象，`key`是职位名称，`value`是该职位的详细描述。
    将你在第1步中提取的关键信息与`<candidate>`中的每个职位名称及其描述进行比较。
    你可以参考`value`中的职位描述来加深理解，但最终输出结果中不能包含`value`的任何文字。

3.  **筛选推荐**:
    **特殊规则**: 如果`<query>`中描述的职业领域比较宽泛（例如“运营”），而`<candidate>`中有多个属于该领域的具体职位（例如“产品运营”、“内容运营”），你必须推荐所有相关的具体职位。
    **结果格式**: 一个JSON对象。
        - `key`: 推荐的职位名称。
        - `value`: 一个0.0到1.0之间的浮点数，表示置信度（取值参考如下）。
            - `1.0`: 完全匹配 (核心意图与职位明确一致)。
            - `0.8`: 高度匹配 (核心意图与职位高度相关)。
            - `0.6`: 一般匹配 (部分意图与职位相关)。
            - `< 0.6`: 不推荐，不要出现在结果中。

# 输出规定
一个JSON对象, 记录每一步工作流程的结果，格式如下
{
    "分析搜索信息": "",
    "评估候选职位": "",
    "筛选推荐": {}
}
"""


def user_prompt(queries, occs, info):
    user_prompts = list()
    for query, occ in zip(queries, occs):
        user_prompt = '{"query": "%s", "candidate": %s}' % (query, occ)
        user_prompts.append(user_prompt)
    return user_prompts


async def concurrency(user_prompts, model):
    try:
        async with aiohttp.ClientSession() as session:
            tasks = [local_chat_async(session, system_prompt, user_prompt) for user_prompt in user_prompts]
            # tasks = [newapi_chat_async(session, system_prompt, user_prompt, model) for user_prompt in user_prompts]
            results = await tqdm_asyncio.gather(*tasks)
        return list(results)
    except Exception:
        return None


async def main(file, info):
    model = "deepseek"
    chunksize = 60
    reader = pandas.read_csv(file, chunksize=chunksize)
    total = sum([1 for _ in reader])
    reader.close()

    data = list()
    reader = pandas.read_csv(file, chunksize=chunksize)
    for chunk in tqdm(reader, total=total):
        queries = chunk["query"].to_list()
        occs = [eval(x) for x in chunk["type3"].to_list()]
        results = await concurrency(queries, occs, model)
        if results is None:
            continue

        time.sleep(1)

        for query, result in zip(queries, results):
            try:
                result = json_repair.loads(result)
                if result["label"] == "yes":
                    data.append(query)
            except Exception:
                continue

    pandas.DataFrame(data, columns=["query"]).to_csv(f"./data/query_llm.csv", index=False, lineterminator="\r\n")


if __name__ == "__main__":
    info = pandas.read_csv("./data/occ_describe_graph.csv")
    asyncio.run(main("./data/yupao_info_type3.csv", info))
