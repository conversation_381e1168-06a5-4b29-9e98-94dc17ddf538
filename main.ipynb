# import os
# for root, dirs, files in os.walk("D:/Java/nlu-ner-process/nlu-ner-process-web/src/test/resources/all_occ"):

import pandas

yupao = pandas.read_csv("./data/yupao_occ_all.csv")
boss = pandas.read_csv("./data/boss_occ.csv")
yupao.shape, boss.shape

from ast import literal_eval

align_yupao, align_boss = list(), list()
for i,row in yupao.iterrows():
    boss_occ = literal_eval(row["boss"])
    if len(boss_occ) > 0:
        align_yupao.append(row["occupation_name"])
        align_boss.extend(boss_occ)

residual_yupao = [occ for occ in yupao["occupation_name"].tolist() if occ not in align_yupao]
residual_boss = [occ for occ in boss["occ_name"].tolist() if occ not in align_boss]

from tool.api import newapi_embed
from tqdm.auto import tqdm
import json

data = dict()
for text in tqdm(residual_yupao, total=len(residual_yupao)):
    try:
        data[text] = newapi_embed([text], "Qwen/Qwen3-Embedding-8B")[0]
    except Exception as e:
        data[text] = list()
        print(e)
with open("./data/yupao_occ_vector.json", "w") as file:
    json.dump(data, file, ensure_ascii=False, indent=4)

data = dict()
for text in tqdm(residual_boss, total=len(residual_boss)):
    try:
        data[text] = newapi_embed([text], "Qwen/Qwen3-Embedding-8B")[0]
    except Exception as e:
        data[text] = list()
        print(e)
with open("./data/boss_occ_vector.json", "w") as file:
    json.dump(data, file, ensure_ascii=False, indent=4)

from tool.api import newapi_rerank
import json
from sentence_transformers import util
from tqdm.auto import tqdm

with open("./data/yupao_occ_vector.json", "r") as file:
    yupao_occ_vector = json.load(file)
with open("./data/boss_occ_vector.json", "r") as file:
    boss_occ_vector = json.load(file)
boss_occs, boss_vectors = list(boss_occ_vector.keys()), list(boss_occ_vector.values())

recall = dict()
for yupao_occ, yupao_vector in tqdm(yupao_occ_vector.items(), total=len(yupao_occ_vector)):
    scores = list()
    similarities = util.cos_sim(yupao_vector, boss_vectors)
    occ_similarity = [(boss_occ, similarity) for boss_occ, similarity in zip(boss_occs, similarities[0])]
    occ_similarity.sort(key=lambda x: x[1], reverse=True)
    recall[yupao_occ] = [occ for occ, _ in occ_similarity[:20]]

rerank = dict()
for yupao_occ, boss_occs in tqdm(recall.items(), total=len(recall)):
    try:
        result = newapi_rerank(yupao_occ, boss_occs, "Qwen/Qwen3-Reranker-8B")
        rerank[yupao_occ] = [occ for occ, _ in result[:10]]
    except Exception as e:
        rerank[yupao_occ] = list()
        print(e)

with open("./data/yupao_boss.json", "w") as file:
    json.dump(rerank, file, ensure_ascii=False, indent=4)

from tool.api import local_chat
import json_repair

with open("./data/yupao_boss.json", "r") as file:
    data = json.load(file)
    
system_prompt = """{
    "background": "",
    "role": "",
    "input args": {
        "aocc": "str, 一个来自A平台的职业岗位",
        "bocc": "list[str], 一批来自B平台的职业岗位"
    },
    "task": "从bocc中找到能与aocc对齐、互换使用的职业岗位, 没有则返回空列表",
    "json output format": {
        "result": []
    }
}"""

system_prompt = """{
    "background": "我们正在进行一项职业岗位对齐任务，目的是将来自不同招聘平台（A平台和B平台）的相似或等同的职业岗位进行匹配。这有助于我们进行数据整合和分析。",
    "role": "你是一个专业的职业岗位分析师，擅长理解和比较不同描述下的岗位职责和要求，并找出它们之间的等价关系。",
    "input args": {
        "aocc": "str, 一个来自A平台的职业岗位名称",
        "bocc": "list[str], 一批来自B平台的职业岗位名称列表"
    },
    "task": "你的任务是：对于给定的A平台职业岗位(aocc)，从B平台的职业岗位列表(bocc)中，精确地找出所有可以与之对齐或互换的岗位。'对齐'或'互换'意味着这两个岗位在核心职责、技能要求和层级上高度相似，可以被认为是同一个职位。如果bocc中没有能与aocc匹配的岗位，则返回一个空列表。",
    "constraints": "1. 严格按照指定的JSON格式输出，不要添加任何额外的解释或说明文字。\\n2. 匹配必须是精确的，如果只是部分相关或层级不同（如 '软件工程师' vs '高级软件工程师'），则不应视为匹配。\\n3. 只需考虑岗位名称的字面意思和行业通用认知，不要做过度引申。",
    "json output format": {
        "result": ["string"]
    },
    "examples": [
        {
            "input": {
                "aocc": "软件开发工程师",
                "bocc": ["软件工程师", "项目经理", "Software Engineer", "产品经理"]
            },
            "output": {
                "result": ["软件工程师", "Software Engineer"]
            }
        },
        {
            "input": {
                "aocc": "市场专员",
                "bocc": ["销售代表", "运营专员", "市场经理"]
            },
            "output": {
                "result": []
            }
        }
    ]
}"""

import random

key = random.choice(list(data.keys()))
user_prompt = '{"aocc": "%s", "bocc": %s}' % (key, data[key])
result = local_chat(system_prompt, user_prompt, 0.7, 0.8)
result = json_repair.loads(result)
result