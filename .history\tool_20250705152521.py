import requests
import re
import opencc
import unicodedata
import hanlp

converter = opencc.OpenCC('t2s.json')   
char = r'0-9a-zA-Z \u4e00-\u9fff'   # 数字、英文、空格、汉字
punctuation = r"""!"#$%&'()*+,-./:;<=>?@[\]^_`{|}~【】《》——、，。？！"""   # 标点符号
pattern = f'[^{char}{punctuation}]' 
model = hanlp.load(hanlp.pretrained.mtl.OPEN_TOK_POS_NER_SRL_DEP_SDP_CON_ELECTRA_BASE_ZH)


def preprocess(raw):
    # 正则过滤
    raw = re.sub(pattern, ' ', raw)
    raw = re.sub(r'\s+', ' ', raw)

    # 合法 unicode 字符
    text = ''
    for item in raw.strip():
        try:
            if unicodedata.name(item):
                text += item
        except ValueError:
            continue
    
    # 全角 → 半角
    text = unicodedata.normalize('NFKC', text)

    # 繁体字 → 简体字
    text = converter.convert(text)

    # 删除地名
    result = model(text, tasks='ner*')
    for item in result['ner']:
        if item[1] in ['LOCATION', 'ORGANIZATION']:
            text = text.replace(item[0], '')
    return text


local_headers = {'Content-Type': 'application/json'}
local_chat_url = 'http://*************:8000/v1/chat/completions'
local_chat_model = 'Qwen3-32B-AWQ'

newapi_headers = {"Content-Type": "application/json", "Authorization": "Bearer sk-YqYDP9b8yoDlyOZP1TON53KkePMtJsySS5Wn8iCmvlM5Xxcy"}
newapi_chat_url = "https://api-llm.yupaowang.com/v1/chat/completions"
newapi_embed_url = "https://api-llm.yupaowang.com/v1/embeddings"
newapi_chat_model = {'doubao': 'doubao-1-5-pro-32k-250115', 'qwen': 'qwen-plus', 'deepseek': 'deepseek-v3-250324'}
newapi_embed_model = 'text-embedding-v3'

temperature = 0.6

newapi_rerank_url = "https://api-llm.yupaowang.com/v1/rerank"


def local_chat(system_prompt, user_prompt):
    data = {
        'model': local_chat_model,
        'temperature': temperature,
        "chat_template_kwargs": {"enable_thinking": True},
        'messages': [
            {'role': 'system', 'content': system_prompt},
            {'role': 'user', 'content': user_prompt}
        ],
        "response_format": {"type": "json_object"}
    }
    response = requests.post(local_chat_url, headers=local_headers, json=data)
    result = response.json()
    return result['choices'][0]['message']['content']


async def local_chat_async(session, system_prompt, user_prompt):
    data = {
        'model': local_chat_model,
        'temperature': temperature,
        "chat_template_kwargs": {"enable_thinking": False},
        'messages': [
            {'role': 'system', 'content': system_prompt},
            {'role': 'user', 'content': user_prompt}
        ],
        "response_format": {"type": "json_object"}
    }
    async with session.post(local_chat_url, headers=local_headers, json=data) as response:
        result = await response.json()
        return result['choices'][0]['message']['content']


def newapi_chat(system_prompt, user_prompt, model):
    data = {
        'model': newapi_chat_model[model],
        'temperature': temperature,
        'messages': [
            {'role': 'system', 'content': system_prompt},
            {'role': 'user', 'content': user_prompt}
        ],
        "response_format": {"type": "json_object"}
    }
    response = requests.post(newapi_chat_url, headers=newapi_headers, json=data)
    result = response.json()
    return result['choices'][0]['message']['content']


async def newapi_chat_async(session, system_prompt, user_prompt, model):
    data = {
        'model': newapi_chat_model[model],
        'temperature': temperature,
        'messages': [
            {'role': 'system', 'content': system_prompt},
            {'role': 'user', 'content': user_prompt}
        ],
        "response_format": {"type": "json_object"}
    }
    async with session.post(newapi_chat_url, headers=newapi_headers, json=data) as response:
        result = await response.json()
        return result['choices'][0]['message']['content']


def newapi_embed(texts):
    '''
    Args:
        texts (list): 文本列表, 最多10个

    Returns:
        list
    '''
    data = {'model': newapi_embed_model, 'input': texts}
    response = requests.post(newapi_embed_url, headers=newapi_headers, json=data)
    result = response.json()
    embeddings = [item['embedding'] for item in result['data']]
    return embeddings
    

def yupao_occ(info, th=0.45):
    url = 'http://yupao-test-intranet.yupaowang.com/algorithm/intranet/models/jobOccClassifyV1'
    headers = {
        'Accept': '*/*',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json',
        'User-Agent': 'PostmanRuntime-ApipostRuntime/1.1.0'
    }
    data = {'infoDetail': info, 'th': th}
    response = requests.post(url, headers=headers, json=data)
    result = response.json()
    isJD = (result['data']['extraInfo']['isOcc']['id'], result['data']['extraInfo']['isOcc']['probability'])
    predict = [(item['id'], item['probability']) for item in result['data']['occInfoList']]
    hy = (result['data']['extraInfo']['isHuoyuan']['id'], result['data']['extraInfo']['isHuoyuan']['probability'])
    return isJD, predict, hy