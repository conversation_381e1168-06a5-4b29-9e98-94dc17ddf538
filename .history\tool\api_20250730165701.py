import requests

local_headers = {"Content-Type": "application/json"}
local_chat_url = "http://10.254.80.229:8000/v1/chat/completions"
local_chat_model = "Qwen3-30B-A3B-FP8"

newapi_headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer sk-YqYDP9b8yoDlyOZP1TON53KkePMtJsySS5Wn8iCmvlM5Xxcy",
}
newapi_chat_url = "https://api-llm.yupaowang.com/v1/chat/completions"
newapi_embed_url = "https://api-llm.yupaowang.com/v1/embeddings"
newapi_chat_model = {"doubao": "doubao-1-5-pro-32k-250115", "qwen": "qwen-plus", "deepseek": "deepseek-v3-250324"}
newapi_rerank_url = "https://api-llm.yupaowang.com/v1/rerank"


def local_chat(system_prompt, user_prompt, temperature=0.6):
    data = {
        "model": local_chat_model,
        "temperature": temperature,
        "chat_template_kwargs": {"enable_thinking": True},
        "messages": [{"role": "system", "content": system_prompt}, {"role": "user", "content": user_prompt}],
        "response_format": {"type": "json_object"},
    }
    response = requests.post(local_chat_url, headers=local_headers, json=data)
    result = response.json()
    return result["choices"][0]["message"]["content"]


async def local_chat_async(session, system_prompt, user_prompt, temperature=0.6):
    data = {
        "model": local_chat_model,
        "temperature": temperature,
        "chat_template_kwargs": {"enable_thinking": True},
        "messages": [{"role": "system", "content": system_prompt}, {"role": "user", "content": user_prompt}],
        "response_format": {"type": "json_object"},
    }
    async with session.post(local_chat_url, headers=local_headers, json=data) as response:
        result = await response.json()
        return result["choices"][0]["message"]["content"]


def newapi_chat(system_prompt, user_prompt, model, temperature=0.6):
    data = {
        "model": newapi_chat_model[model],
        "temperature": temperature,
        "messages": [{"role": "system", "content": system_prompt}, {"role": "user", "content": user_prompt}],
        "response_format": {"type": "json_object"},
    }
    response = requests.post(newapi_chat_url, headers=newapi_headers, json=data)
    result = response.json()
    return result["choices"][0]["message"]["content"]


async def newapi_chat_async(session, system_prompt, user_prompt, model, temperature=0.6):
    data = {
        "model": newapi_chat_model[model],
        "temperature": temperature,
        "messages": [{"role": "system", "content": system_prompt}, {"role": "user", "content": user_prompt}],
        "response_format": {"type": "json_object"},
    }
    async with session.post(newapi_chat_url, headers=newapi_headers, json=data) as response:
        result = await response.json()
        return result["choices"][0]["message"]["content"]


def newapi_embed(texts, model):
    """
    Args:
        texts (list): 文本列表, 最多10个
        model (str):

    Returns:
        list
    """
    data = {"model": model, "input": texts}
    response = requests.post(newapi_embed_url, headers=newapi_headers, json=data)
    result = response.json()
    embeddings = [item["embedding"] for item in result["data"]]
    return embeddings


def newapi_rerank(query, documents, model):
    """
    Args:
        query (str):
        documents (list):
        model (str):
    """
    data = {"model": model, "query": query, "documents": documents}
    response = requests.post(newapi_rerank_url, headers=newapi_headers, json=data)
    response.raise_for_status()
    result = response.json()
    return result["results"]


def yupao_occ(info_type, via, title, info_detail, th=0.45):
    url = "http://yupao-test-intranet.yupaowang.com/algorithm/intranet/models/jobOccClassifyV1"
    headers = {
        "Accept": "*/*",
        "Accept-Encoding": "gzip, deflate, br",
        "Connection": "keep-alive",
        "Content-Type": "application/json",
        "User-Agent": "PostmanRuntime-ApipostRuntime/1.1.0",
    }
    data = {"infoType": info_type, "via": via, "title": title, "infoDetail": info_detail, "th": th}
    response = requests.post(url, headers=headers, json=data)
    result = response.json()
    isJD = (result["data"]["extraInfo"]["isOcc"]["id"], result["data"]["extraInfo"]["isOcc"]["probability"])
    predict = [(item["id"], item["probability"]) for item in result["data"]["occInfoList"]]
    hy = (result["data"]["extraInfo"]["isHuoyuan"]["id"], result["data"]["extraInfo"]["isHuoyuan"]["probability"])
    return isJD, predict, hy
